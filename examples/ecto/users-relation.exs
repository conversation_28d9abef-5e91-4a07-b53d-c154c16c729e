Code.require_file("examples/setup.exs")

_pid = ExampleSetup.setup_database([Test.Ecto.TestSchemas.UserSchema])

defmodule Users do
  use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
end

{:ok, user} = Users.insert(%{name: "<PERSON>", email: "<EMAIL>"})

user = Users.get(user.id)
IO.puts("User created with plain map:")
IO.inspect(user)

user_struct = Users.struct(%{name: "<PERSON>", email: "<EMAIL>"})
{:ok, user2} = Users.insert(user_struct)

user2 = Users.get(user2.id)
IO.puts("\nUser created with struct:")
IO.inspect(user2)
