defmodule Drops.Relation.SQL.Introspector.Database.SQLite do
  @moduledoc """
  SQLite implementation of the Database behavior for schema introspection.

  This module provides SQLite-specific implementations for database introspection
  operations using SQLite's PRAGMA statements and system tables.

  ## Features

  - Index introspection via PRAGMA statements
  - Column metadata extraction via PRAGMA table_info
  - SQLite type to Ecto type conversion
  - Support for unique and composite indices
  """

  @behaviour Drops.Relation.SQL.Introspector.Database

  alias Drops.Relation.Schema.{Index, Indices}

  @impl true
  def get_table_indices(repo, table_name) do
    # SQLite PRAGMA to get index list
    index_list_query = "PRAGMA index_list(#{table_name})"

    case repo.query(index_list_query) do
      {:ok, %{rows: rows}} ->
        # PRAGMA index_list returns: [seq, name, unique, origin, partial]
        indices =
          for [_seq, name, unique, _origin, _partial] <- rows do
            # Get index details
            index_info_query = "PRAGMA index_info(#{name})"

            case repo.query(index_info_query) do
              {:ok, %{rows: info_rows}} ->
                # PRAGMA index_info returns: [seqno, cid, name]
                field_names =
                  info_rows
                  # Sort by seqno
                  |> Enum.sort_by(&hd/1)
                  |> Enum.map(fn [_seqno, _cid, field_name] ->
                    String.to_atom(field_name)
                  end)

                Index.from_names(name, field_names, unique == 1, :btree)

              {:error, _} ->
                # If we can't get field info, create index with empty fields
                Index.from_names(name, [], unique == 1, :btree)
            end
          end
          |> Enum.reject(&is_nil/1)

        {:ok, Indices.new(indices)}

      {:error, error} ->
        {:error, error}
    end
  end

  @impl true
  def introspect_table_columns(repo, table_name) do
    # Use SQLite PRAGMA table_info to get column information
    query = "PRAGMA table_info(#{table_name})"

    case repo.query(query) do
      {:ok, %{rows: rows, columns: _columns}} ->
        # PRAGMA table_info returns: [cid, name, type, notnull, dflt_value, pk]
        Enum.map(rows, fn [_cid, name, type, notnull, _dflt_value, pk] ->
          %{
            name: name,
            type: type,
            not_null: notnull == 1,
            primary_key: pk == 1
          }
        end)

      {:error, error} ->
        raise "Failed to introspect table #{table_name}: #{inspect(error)}"
    end
  end

  @impl true
  def db_type_to_ecto_type(sqlite_type, field_name) do
    case String.upcase(sqlite_type) do
      "INTEGER" ->
        # Check if this is likely a boolean field based on field name
        if is_boolean_field?(field_name) do
          :boolean
        else
          :integer
        end

      "TEXT" ->
        :string

      "REAL" ->
        :float

      "NUMERIC" ->
        :float

      "BLOB" ->
        :binary

      "DATETIME" ->
        :naive_datetime

      "DATE" ->
        :date

      "TIME" ->
        :time

      "BOOLEAN" ->
        :boolean

      "JSON" ->
        :map

      # Default to string for unknown types
      _ ->
        :string
    end
  end

  # Helper function to detect boolean fields by name
  defp is_boolean_field?(field_name) when is_binary(field_name) do
    field_name_lower = String.downcase(field_name)

    # Common boolean field patterns
    (String.ends_with?(field_name_lower, "_field") and
       String.contains?(field_name_lower, "boolean")) or
      String.ends_with?(field_name_lower, "?") or
      field_name_lower in [
        "active",
        "enabled",
        "disabled",
        "visible",
        "hidden",
        "published",
        "deleted",
        "archived"
      ]
  end

  defp is_boolean_field?(_), do: false

  @impl true
  def index_type_to_atom(type_string) do
    case String.downcase(type_string) do
      "" -> nil
      "btree" -> :btree
      # SQLite primarily uses btree indices, but return nil for unknown types
      _ -> nil
    end
  end
end
