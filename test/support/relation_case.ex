defmodule Drops.RelationCase do
  @moduledoc """
  Test case template for Drops.Relation tests.

  This module provides a convenient way to test relation modules with automatic
  schema cache management and relation setup.

  ## Usage

      defmodule MyRelationTest do
        use Drops.RelationCase, async: true

        describe "my relation tests" do
          @tag relations: [:users]
          test "basic test", %{users: users} do
            # users relation is automatically available
          end

          # Or use the relation macro directly
          relation(:posts)

          test "posts test", %{posts: posts} do
            # posts relation is available
          end
        end
      end

  ## Features

  - Automatic Ecto sandbox setup
  - Schema cache clearing for test isolation
  - Relation macro for defining test relations
  - Support for @tag relations: [...] and @describetag relations: [...]
  - Automatic migration running for test tables
  """

  use ExUnit.CaseTemplate

  using do
    quote do
      use Drops.ContractCase

      alias Drops.TestRepo

      import Ecto
      import Ecto.Changeset
      import Ecto.Query
      import Drops.RelationCase

      # Clear schema cache before each test
      setup do
        if Process.whereis(Drops.Relation.SchemaCache) do
          Drops.Relation.SchemaCache.clear_all()
        end

        :ok
      end
    end
  end

  setup tags do
    # Set up Ecto sandbox
    setup_sandbox(tags)

    # Run migrations for test environment
    run_migrations()

    # Handle relation tags
    context = handle_relation_tags(tags)

    {:ok, context}
  end

  @doc """
  Defines a relation for testing.

  ## Examples

      relation(:users)
      relation(:posts) do
        # Custom relation configuration
      end
  """
  defmacro relation(name, _opts \\ []) do
    relation_name = name |> Atom.to_string() |> Macro.camelize()
    module_name = Module.concat([Test, Relations, relation_name])
    table_name = Atom.to_string(name)

    quote do
      setup context do
        # Define the relation module
        defmodule unquote(module_name) do
          use Drops.Relation, repo: Drops.TestRepo, name: unquote(table_name), infer: true
        end

        # Add relation to context
        relation_context = Map.put(context, unquote(name), unquote(module_name))

        on_exit(fn ->
          # Clean up the module
          try do
            :code.purge(unquote(module_name))
            :code.delete(unquote(module_name))
          rescue
            _ -> :ok
          end
        end)

        {:ok, relation_context}
      end
    end
  end

  @doc """
  Sets up the sandbox based on the test tags.
  """
  def setup_sandbox(tags) do
    pid = Ecto.Adapters.SQL.Sandbox.start_owner!(Drops.TestRepo, shared: not tags[:async])
    on_exit(fn -> Ecto.Adapters.SQL.Sandbox.stop_owner(pid) end)
  end

  @doc """
  Runs all test migrations.
  """
  def run_migrations do
    # Run migrations if they haven't been run yet
    case Ecto.Adapters.SQL.query(
           Drops.TestRepo,
           "SELECT name FROM sqlite_master WHERE type='table' AND name='users';"
         ) do
      {:ok, %{rows: []}} ->
        # Tables don't exist, run migrations
        Ecto.Migrator.run(Drops.TestRepo, migrations_path(), :up, all: true)

      _ ->
        # Tables already exist, skip
        :ok
    end
  end

  @doc """
  Handles @tag relations: [...] and @describetag relations: [...] syntax.
  """
  def handle_relation_tags(tags) do
    relations = tags[:relations] || []

    Enum.reduce(relations, %{}, fn relation_name, context ->
      relation_name_string = Atom.to_string(relation_name)
      relation_module_name = relation_name_string |> Macro.camelize()
      module_name = Module.concat([Test, Relations, relation_module_name])

      # Define the relation module dynamically
      # We need to use Module.create/3 for runtime module creation
      {:module, ^module_name, _bytecode, _result} =
        Module.create(
          module_name,
          quote do
            use Drops.Relation,
              repo: Drops.TestRepo,
              name: unquote(relation_name_string),
              infer: true
          end,
          Macro.Env.location(__ENV__)
        )

      # Add to context
      Map.put(context, relation_name, module_name)
    end)
  end

  defp migrations_path do
    Application.app_dir(:drops, "priv/test/repo/migrations")
  end
end
